using System.ComponentModel.DataAnnotations;

namespace Rolla.BuildingBlocks.Application.Common;

public class PaginationRequest
{
    private const int MaxPageSize = 100;
    private const int DefaultPageSize = 10;

    [Range(1, int.MaxValue, ErrorMessage = "Page number must be greater than 0")]
    public int PageNumber { get; set; } = 1;

    private int _pageSize = DefaultPageSize;

    [Range(1, MaxPageSize, ErrorMessage = "Page size must be between 1 and 100")]
    public int PageSize
    {
        get => _pageSize;
        set => _pageSize = value > MaxPageSize ? MaxPageSize : value;
    }

    public int Skip => (PageNumber - 1) * PageSize;
    public int Take => PageSize;
}
