using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Rolla.BuildingBlocks.Application.Common;
using Rolla.BuildingBlocks.Application.Extensions;
using Rolla.BuildingBlocks.Application.Validation;

namespace Rolla.BuildingBlocks.Application.Controllers;

[ApiController]
[Produces("application/json")]
public abstract class BaseController : ControllerBase
{
    protected readonly ILogger Logger;

    protected BaseController(ILogger logger)
    {
        Logger = logger;
    }

    protected IActionResult ApiOk<T>(T data, string? message = null)
    {
        var result = ApiResult<T>.Ok(data, message);
        return Ok(result);
    }

    protected IActionResult ApiCreated<T>(T data, string? message = null)
    {
        var result = ApiResult<T>.Created(data, message);
        return StatusCode(201, result);
    }

    protected IActionResult ApiNoContent(string? message = null)
    {
        var result = ApiResult.NoContent(message);
        return StatusCode(204, result);
    }

    protected IActionResult ApiNotFound(string? message = null)
    {
        var result = ApiResult.NotFound(message);
        return NotFound(result);
    }

    protected IActionResult ApiBadRequest(IEnumerable<string> errors, string? message = null)
    {
        var result = ApiResult.BadRequest(errors, message);
        return BadRequest(result);
    }

    protected IActionResult ApiBadRequest(string error, string? message = null)
    {
        return ApiBadRequest([error], message);
    }

    protected IActionResult ApiInternalServerError(string? message = null)
    {
        var result = ApiResult.InternalServerError(message);
        return StatusCode(500, result);
    }

    protected IActionResult HandleResult<T>(Result<T> result, string? successMessage = null)
    {
        return result.ToApiResult(successMessage);
    }

    protected IActionResult HandleCreatedResult<T>(Result<T> result, string? successMessage = null)
    {
        return result.ToApiCreatedResult(successMessage);
    }

    protected IActionResult HandleNotFoundResult<T>(Result<T> result, string? notFoundMessage = null)
    {
        return result.ToApiNotFoundResult(notFoundMessage);
    }

    protected IActionResult HandleNoContentResult(Result<bool> result, string? successMessage = null)
    {
        return result.ToApiNoContentResult(successMessage);
    }

    protected async Task<IActionResult> ExecuteAsync<T>(Func<Task<Result<T>>> operation, string? successMessage = null, string? errorMessage = null)
    {
        try
        {
            var result = await operation();
            return HandleResult(result, successMessage);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, errorMessage ?? "An error occurred during operation");
            return ApiInternalServerError(errorMessage ?? "An unexpected error occurred");
        }
    }

    protected async Task<IActionResult> ExecuteCreatedAsync<T>(Func<Task<Result<T>>> operation, string? successMessage = null, string? errorMessage = null)
    {
        try
        {
            var result = await operation();
            return HandleCreatedResult(result, successMessage);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, errorMessage ?? "An error occurred during creation");
            return ApiInternalServerError(errorMessage ?? "An unexpected error occurred");
        }
    }

    protected async Task<IActionResult> ExecuteNotFoundAsync<T>(Func<Task<Result<T>>> operation, string? notFoundMessage = null, string? errorMessage = null)
    {
        try
        {
            var result = await operation();
            return HandleNotFoundResult(result, notFoundMessage);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, errorMessage ?? "An error occurred during operation");
            return ApiInternalServerError(errorMessage ?? "An unexpected error occurred");
        }
    }

    protected async Task<IActionResult> ExecuteNoContentAsync(Func<Task<Result<bool>>> operation, string? successMessage = null, string? errorMessage = null)
    {
        try
        {
            var result = await operation();
            return HandleNoContentResult(result, successMessage);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, errorMessage ?? "An error occurred during operation");
            return ApiInternalServerError(errorMessage ?? "An unexpected error occurred");
        }
    }
}
