using Rolla.BuildingBlocks.Application.Modules;

using Rolla.Modules.RollaAdmin.Api.Module;

namespace Rolla.API.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddRollaAdminModule(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        IModule module = new RollaAdminModule();
        module.RegisterModule(services, configuration);
        return services;
    }
}

public static class ApplicationBuilderExtensions
{
    public static WebApplication UseRollaAdminModule(this WebApplication app)
    {
        IModule module = new RollaAdminModule();
        module.UseModule(app);
        return app;
    }
}
